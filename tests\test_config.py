"""
配置管理测试
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import yaml

from doc2md.config import ConfigManager


class TestConfigManager(unittest.TestCase):
    """ConfigManager测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        """测试后清理"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_default_config(self):
        """测试默认配置"""
        config_manager = ConfigManager()
        
        # 验证默认值
        self.assertTrue(config_manager.get('output.preserve_images'))
        self.assertTrue(config_manager.get('output.extract_tables'))
        self.assertEqual(config_manager.get('output.encoding'), 'utf-8')
        self.assertEqual(config_manager.get('processing.max_workers'), 4)
        self.assertEqual(config_manager.get('logging.level'), 'INFO')
    
    def test_load_config(self):
        """测试加载配置"""
        # 创建测试配置文件
        config_path = self.temp_dir / "test_config.yaml"
        test_config = {
            'output': {
                'preserve_images': False,
                'include_metadata': True
            },
            'processing': {
                'max_workers': 8
            }
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(test_config, f)
        
        # 加载配置
        config_manager = ConfigManager(config_path)
        
        # 验证加载的值
        self.assertFalse(config_manager.get('output.preserve_images'))
        self.assertTrue(config_manager.get('output.include_metadata'))
        self.assertEqual(config_manager.get('processing.max_workers'), 8)
        
        # 验证默认值仍然存在
        self.assertTrue(config_manager.get('output.extract_tables'))
        self.assertEqual(config_manager.get('output.encoding'), 'utf-8')
    
    def test_get_config(self):
        """测试获取配置值"""
        config_manager = ConfigManager()
        
        # 测试嵌套键
        self.assertEqual(config_manager.get('output.encoding'), 'utf-8')
        
        # 测试默认值
        self.assertEqual(config_manager.get('non_existent_key', 'default'), 'default')
        self.assertEqual(config_manager.get('output.non_existent', 123), 123)
    
    def test_set_config(self):
        """测试设置配置值"""
        config_manager = ConfigManager()
        
        # 设置现有值
        config_manager.set('output.preserve_images', False)
        self.assertFalse(config_manager.get('output.preserve_images'))
        
        # 设置新值
        config_manager.set('output.new_option', 'value')
        self.assertEqual(config_manager.get('output.new_option'), 'value')
        
        # 设置嵌套新值
        config_manager.set('new_section.nested.value', 42)
        self.assertEqual(config_manager.get('new_section.nested.value'), 42)
    
    def test_save_config(self):
        """测试保存配置"""
        config_manager = ConfigManager()
        
        # 修改配置
        config_manager.set('output.preserve_images', False)
        config_manager.set('processing.max_workers', 8)
        
        # 保存配置
        config_path = self.temp_dir / "saved_config.yaml"
        config_manager.save_config(config_path)
        
        # 验证文件存在
        self.assertTrue(config_path.exists())
        
        # 加载保存的配置
        loaded_config_manager = ConfigManager(config_path)
        
        # 验证值
        self.assertFalse(loaded_config_manager.get('output.preserve_images'))
        self.assertEqual(loaded_config_manager.get('processing.max_workers'), 8)
    
    def test_merge_config(self):
        """测试合并配置"""
        base_config = {
            'a': 1,
            'b': {
                'c': 2,
                'd': 3
            }
        }
        
        update_config = {
            'a': 10,
            'b': {
                'c': 20
            },
            'e': 30
        }
        
        config_manager = ConfigManager()
        config_manager._merge_config(base_config, update_config)
        
        # 验证合并结果
        self.assertEqual(base_config['a'], 10)  # 覆盖
        self.assertEqual(base_config['b']['c'], 20)  # 覆盖
        self.assertEqual(base_config['b']['d'], 3)  # 保留
        self.assertEqual(base_config['e'], 30)  # 新增


if __name__ == '__main__':
    unittest.main()
