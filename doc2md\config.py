"""
配置管理模块
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional


class ConfigManager:
    """配置管理器"""
    
    DEFAULT_CONFIG = {
        'output': {
            'preserve_images': True,
            'extract_tables': True,
            'include_metadata': False,
            'encoding': 'utf-8'
        },
        'processing': {
            'max_workers': 4,
            'timeout': 300,
            'recursive': False
        },
        'logging': {
            'level': 'INFO',
            'file': None,
            'console': True
        },
        'docling': {
            'enable_ocr': False,
            'image_quality': 'medium'
        }
    }
    
    def __init__(self, config_path: Optional[Path] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self.DEFAULT_CONFIG.copy()
        
        if config_path and config_path.exists():
            self.load_config(config_path)
    
    def load_config(self, config_path: Path) -> None:
        """
        从文件加载配置
        
        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = yaml.safe_load(f)
            
            if user_config:
                self._merge_config(self.config, user_config)
            
            logging.info(f"配置文件加载成功: {config_path}")
            
        except Exception as e:
            logging.warning(f"加载配置文件失败 {config_path}: {e}")
    
    def _merge_config(self, base: Dict[str, Any], update: Dict[str, Any]) -> None:
        """
        递归合并配置字典
        
        Args:
            base: 基础配置
            update: 更新配置
        """
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键（支持点分隔的嵌套键）
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键（支持点分隔的嵌套键）
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self, config_path: Optional[Path] = None) -> None:
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件路径
        """
        if config_path is None:
            config_path = self.config_path
        
        if config_path is None:
            raise ValueError("未指定配置文件路径")
        
        try:
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logging.info(f"配置文件保存成功: {config_path}")
            
        except Exception as e:
            logging.error(f"保存配置文件失败 {config_path}: {e}")
            raise
    
    def setup_logging(self) -> None:
        """根据配置设置日志"""
        log_config = self.config.get('logging', {})
        
        # 设置日志级别
        level = getattr(logging, log_config.get('level', 'INFO').upper())
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 清除现有处理器
        root_logger = logging.getLogger()
        root_logger.handlers.clear()
        root_logger.setLevel(level)
        
        # 控制台处理器
        if log_config.get('console', True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        
        # 文件处理器
        log_file = log_config.get('file')
        if log_file:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
