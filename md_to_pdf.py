#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown转PDF工具
需要安装: pip install markdown pdfkit weasyprint
"""

import markdown
import pdfkit
from weasyprint import HTML, CSS
import os
import sys

def md_to_pdf_pdfkit(md_file, output_file):
    """使用pdfkit转换（需要安装wkhtmltopdf）"""
    try:
        # 读取markdown文件
        with open(md_file, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # 转换为HTML
        html = markdown.markdown(md_content, extensions=['tables', 'codehilite'])
        
        # 添加CSS样式
        html_with_style = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{
                    font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
                    line-height: 1.6;
                    margin: 40px;
                    color: #333;
                }}
                h1 {{
                    color: #2c3e50;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 10px;
                }}
                h2 {{
                    color: #34495e;
                    margin-top: 30px;
                }}
                h3 {{
                    color: #7f8c8d;
                }}
                ul, ol {{
                    margin-left: 20px;
                }}
                code {{
                    background-color: #f8f9fa;
                    padding: 2px 4px;
                    border-radius: 3px;
                }}
                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin: 20px 0;
                }}
                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }}
                th {{
                    background-color: #f2f2f2;
                }}
            </style>
        </head>
        <body>
        {html}
        </body>
        </html>
        """
        
        # 配置选项
        options = {
            'page-size': 'A4',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None
        }
        
        # 转换为PDF
        pdfkit.from_string(html_with_style, output_file, options=options)
        print(f"✅ 成功转换: {output_file}")
        
    except Exception as e:
        print(f"❌ pdfkit转换失败: {e}")
        return False
    return True

def md_to_pdf_weasyprint(md_file, output_file):
    """使用WeasyPrint转换（推荐，纯Python）"""
    try:
        # 读取markdown文件
        with open(md_file, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # 转换为HTML
        html = markdown.markdown(md_content, extensions=['tables', 'codehilite'])
        
        # 添加CSS样式
        html_with_style = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
        </head>
        <body>
        {html}
        </body>
        </html>
        """
        
        # CSS样式
        css = CSS(string="""
            @page {
                size: A4;
                margin: 2cm;
            }
            body {
                font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                font-size: 12pt;
            }
            h1 {
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
                font-size: 24pt;
            }
            h2 {
                color: #34495e;
                margin-top: 30px;
                font-size: 18pt;
            }
            h3 {
                color: #7f8c8d;
                font-size: 14pt;
            }
            ul, ol {
                margin-left: 20px;
            }
            li {
                margin-bottom: 5px;
            }
            code {
                background-color: #f8f9fa;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: "Consolas", "Monaco", monospace;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin: 20px 0;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            strong {
                color: #2c3e50;
            }
        """)
        
        # 转换为PDF
        HTML(string=html_with_style).write_pdf(output_file, stylesheets=[css])
        print(f"✅ 成功转换: {output_file}")
        
    except Exception as e:
        print(f"❌ WeasyPrint转换失败: {e}")
        return False
    return True

def main():
    if len(sys.argv) != 2:
        print("使用方法: python md_to_pdf.py <markdown文件>")
        print("示例: python md_to_pdf.py 王举江_简历.md")
        return
    
    md_file = sys.argv[1]
    if not os.path.exists(md_file):
        print(f"❌ 文件不存在: {md_file}")
        return
    
    # 生成输出文件名
    base_name = os.path.splitext(md_file)[0]
    output_file = f"{base_name}.pdf"
    
    print(f"📄 开始转换: {md_file} -> {output_file}")
    
    # 尝试WeasyPrint（推荐）
    if md_to_pdf_weasyprint(md_file, output_file):
        return
    
    # 如果WeasyPrint失败，尝试pdfkit
    print("🔄 尝试备用方法...")
    md_to_pdf_pdfkit(md_file, output_file)

if __name__ == "__main__":
    main()
