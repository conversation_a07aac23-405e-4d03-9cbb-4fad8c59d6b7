# 图片提取功能修复说明

## 问题描述

之前的版本在提取PPTX和DOCX中的图片时出现错误：
```
FloatingItem.get_image() missing 1 required positional argument: 'doc'
```

## 修复内容

### 1. 更新图片提取API调用

**修复前：**
```python
# 错误的调用方式
picture.get_image()
```

**修复后：**
```python
# 正确的调用方式
picture.get_image(document)
```

### 2. 启用图片生成选项

在转换器初始化时启用必要的图片生成选项：

```python
pdf_options = PdfPipelineOptions()
pdf_options.images_scale = 2.0  # 高质量图片
pdf_options.generate_page_images = True
pdf_options.generate_picture_images = True
pdf_options.generate_table_images = True
```

### 3. 改进图片提取逻辑

- 使用 `document.iterate_items()` 遍历所有元素
- 分别处理 `PictureItem` 和 `TableItem`
- 正确导入 `docling_core.types.doc` 中的类型
- 添加更好的错误处理和日志记录

### 4. 文件结构优化

修复后的图片提取会创建：
```
document.md
document_images/
  ├── document_picture_001.png
  ├── document_picture_002.png
  ├── document_table_001.png
  └── ...
```

## 验证修复

### 快速验证
```bash
python quick_test.py
```

### 完整测试
```bash
python test_image_extraction.py
```

### 实际使用
```bash
python doc2md.py your_document.pptx
```

## 主要改进

1. **API兼容性**: 使用最新的Docling API
2. **错误处理**: 更好的异常捕获和日志记录
3. **图片质量**: 设置高质量图片导出
4. **类型支持**: 同时支持图片和表格提取
5. **文件命名**: 更清晰的文件命名规则

## 依赖要求

确保安装了正确版本的依赖：
```bash
pip install docling>=1.0.0
pip install docling-core
```

## 注意事项

1. 图片提取需要文档中实际包含图片或表格
2. 某些复杂的嵌入对象可能无法提取
3. 图片质量取决于原始文档的质量
4. 大文档可能需要更多内存和处理时间
