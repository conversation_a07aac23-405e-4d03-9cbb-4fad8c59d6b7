#!/usr/bin/env python3
"""
测试图片提取功能
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from doc2md.config import ConfigManager
from doc2md.converter import Doc2MDConverter


def test_image_extraction():
    """测试图片提取功能"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("=== 图片提取测试 ===\n")
    
    # 创建配置
    config_manager = ConfigManager()
    config_manager.set('output.preserve_images', True)
    config_manager.set('logging.level', 'DEBUG')
    
    # 创建转换器
    try:
        converter = Doc2MDConverter(config_manager.config)
        print("✓ 转换器初始化成功")
    except Exception as e:
        print(f"✗ 转换器初始化失败: {e}")
        return False
    
    # 查找测试文件
    test_files = []
    for ext in ['.docx', '.pptx']:
        test_files.extend(Path('.').glob(f'*{ext}'))
    
    if not test_files:
        print("警告: 未找到测试文件 (.docx 或 .pptx)")
        print("请将一些包含图片的DOCX或PPTX文件放在当前目录中")
        return False
    
    print(f"找到 {len(test_files)} 个测试文件:")
    for file in test_files:
        print(f"  - {file}")
    
    # 测试转换
    success_count = 0
    for test_file in test_files[:2]:  # 只测试前两个文件
        print(f"\n--- 测试文件: {test_file} ---")
        
        try:
            output_path = converter.convert_file(test_file)
            print(f"✓ 转换成功: {output_path}")
            
            # 检查图片目录
            images_dir = output_path.parent / f"{output_path.stem}_images"
            if images_dir.exists():
                image_files = list(images_dir.glob('*.png'))
                print(f"✓ 图片目录存在: {images_dir}")
                print(f"✓ 提取了 {len(image_files)} 张图片")
                
                for img_file in image_files[:3]:  # 显示前3张图片
                    print(f"  - {img_file.name}")
            else:
                print("! 未创建图片目录（可能文档中没有图片）")
            
            success_count += 1
            
        except Exception as e:
            print(f"✗ 转换失败: {e}")
            logging.exception("详细错误信息:")
    
    print(f"\n=== 测试结果 ===")
    print(f"成功: {success_count}/{len(test_files[:2])}")
    
    return success_count > 0


if __name__ == "__main__":
    success = test_image_extraction()
    sys.exit(0 if success else 1)
