#!/usr/bin/env python3
"""
快速测试脚本 - 验证图片提取修复
"""

import sys
import logging
from pathlib import Path

def test_docling_import():
    """测试Docling导入"""
    try:
        from docling.document_converter import DocumentConverter
        from docling_core.types.doc import PictureItem, TableItem
        print("✓ Docling库导入成功")
        return True
    except ImportError as e:
        print(f"✗ Docling库导入失败: {e}")
        print("请运行: pip install docling")
        return False

def test_converter_init():
    """测试转换器初始化"""
    try:
        # 添加项目路径
        sys.path.insert(0, str(Path(__file__).parent))
        
        from doc2md.converter import Doc2MDConverter
        from doc2md.config import ConfigManager
        
        config = ConfigManager()
        converter = Doc2MDConverter(config.config)
        print("✓ 转换器初始化成功")
        return True
    except Exception as e:
        print(f"✗ 转换器初始化失败: {e}")
        return False

def test_image_api():
    """测试图片API"""
    try:
        from docling.document_converter import DocumentConverter
        from docling.datamodel.base_models import InputFormat
        from docling.datamodel.pipeline_options import PdfPipelineOptions
        from docling.document_converter import PdfFormatOption
        from docling_core.types.doc import PictureItem, TableItem
        
        # 创建带图片选项的转换器
        pipeline_options = PdfPipelineOptions()
        pipeline_options.images_scale = 2.0
        pipeline_options.generate_picture_images = True
        pipeline_options.generate_table_images = True
        
        converter = DocumentConverter(
            format_options={
                InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)
            }
        )
        
        print("✓ 图片API配置成功")
        return True
    except Exception as e:
        print(f"✗ 图片API配置失败: {e}")
        return False

def find_test_files():
    """查找测试文件"""
    test_files = []
    for ext in ['.docx', '.pptx']:
        test_files.extend(Path('.').glob(f'*{ext}'))
    
    if test_files:
        print(f"✓ 找到 {len(test_files)} 个测试文件:")
        for file in test_files[:3]:
            print(f"  - {file}")
        return test_files
    else:
        print("! 未找到测试文件")
        print("  请将一些DOCX或PPTX文件放在当前目录中进行测试")
        return []

def main():
    """主函数"""
    print("=== Doc2MD 图片提取修复验证 ===\n")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    tests = [
        ("Docling库导入", test_docling_import),
        ("转换器初始化", test_converter_init),
        ("图片API配置", test_image_api),
    ]
    
    passed = 0
    for name, test_func in tests:
        print(f"测试: {name}")
        if test_func():
            passed += 1
        print()
    
    print(f"基础测试结果: {passed}/{len(tests)} 通过")
    
    # 查找测试文件
    print("\n--- 查找测试文件 ---")
    test_files = find_test_files()
    
    if test_files and passed == len(tests):
        print("\n--- 建议的下一步 ---")
        print("1. 运行完整测试:")
        print("   python test_image_extraction.py")
        print("\n2. 或直接转换文件:")
        print(f"   python doc2md.py {test_files[0]}")
        print("\n3. 检查生成的图片目录:")
        print(f"   ls {test_files[0].stem}_images/")
    
    print(f"\n=== 修复状态 ===")
    if passed == len(tests):
        print("✅ 图片提取功能已修复，可以正常使用")
    else:
        print("❌ 仍有问题需要解决")
        print("请检查Docling库是否正确安装")

if __name__ == "__main__":
    main()
