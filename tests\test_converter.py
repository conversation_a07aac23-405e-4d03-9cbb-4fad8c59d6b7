"""
转换器测试
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from doc2md.converter import Doc2MDConverter
from doc2md.exceptions import DocumentConverterError, UnsupportedFormatError


class TestDoc2MDConverter(unittest.TestCase):
    """Doc2MDConverter测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config = {
            'output': {
                'preserve_images': True,
                'include_metadata': False,
                'encoding': 'utf-8'
            },
            'docling': {
                'enable_ocr': False
            }
        }
    
    def tearDown(self):
        """测试后清理"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    @patch('doc2md.converter.DocumentConverter')
    def test_init_converter(self, mock_doc_converter):
        """测试转换器初始化"""
        converter = Doc2MDConverter(self.config)
        self.assertIsNotNone(converter.converter)
        mock_doc_converter.assert_called_once()
    
    def test_is_supported_format(self):
        """测试支持的格式检查"""
        with patch('doc2md.converter.DocumentConverter'):
            converter = Doc2MDConverter(self.config)
            
            # 支持的格式
            self.assertTrue(converter._is_supported_format(Path("test.docx")))
            self.assertTrue(converter._is_supported_format(Path("test.pptx")))
            self.assertTrue(converter._is_supported_format(Path("TEST.DOCX")))
            
            # 不支持的格式
            self.assertFalse(converter._is_supported_format(Path("test.pdf")))
            self.assertFalse(converter._is_supported_format(Path("test.txt")))
            self.assertFalse(converter._is_supported_format(Path("test.doc")))
    
    def test_get_image_extension(self):
        """测试图片扩展名获取"""
        with patch('doc2md.converter.DocumentConverter'):
            converter = Doc2MDConverter(self.config)
            
            # 模拟图片对象
            mock_picture = Mock()
            mock_picture.format = "PNG"
            self.assertEqual(converter._get_image_extension(mock_picture), ".png")
            
            mock_picture.format = "JPEG"
            self.assertEqual(converter._get_image_extension(mock_picture), ".jpg")
            
            # 无格式信息
            mock_picture_no_format = Mock()
            del mock_picture_no_format.format
            self.assertEqual(converter._get_image_extension(mock_picture_no_format), ".png")
    
    def test_clean_markdown(self):
        """测试Markdown清理"""
        with patch('doc2md.converter.DocumentConverter'):
            converter = Doc2MDConverter(self.config)
            
            # 测试多余空行清理
            input_text = "Line 1\n\n\n\nLine 2\n\n\nLine 3"
            expected = "Line 1\n\nLine 2\n\nLine 3"
            result = converter._clean_markdown(input_text)
            self.assertEqual(result, expected)
    
    def test_update_image_references(self):
        """测试图片引用更新"""
        with patch('doc2md.converter.DocumentConverter'):
            converter = Doc2MDConverter(self.config)
            
            markdown_content = "![图片](image_1) 和 ![](image_2)"
            image_mappings = {
                "image_1": "images/pic1.png",
                "image_2": "images/pic2.jpg"
            }
            
            result = converter._update_image_references(markdown_content, image_mappings)
            self.assertIn("![图片](images/pic1.png)", result)
            self.assertIn("![](images/pic2.jpg)", result)
    
    @patch('doc2md.converter.DocumentConverter')
    def test_convert_file_not_exists(self, mock_doc_converter):
        """测试转换不存在的文件"""
        converter = Doc2MDConverter(self.config)
        
        non_existent_file = self.temp_dir / "not_exists.docx"
        
        with self.assertRaises(DocumentConverterError):
            converter.convert_file(non_existent_file)
    
    @patch('doc2md.converter.DocumentConverter')
    def test_convert_file_unsupported_format(self, mock_doc_converter):
        """测试转换不支持的格式"""
        converter = Doc2MDConverter(self.config)
        
        # 创建一个不支持的文件
        unsupported_file = self.temp_dir / "test.txt"
        unsupported_file.write_text("test content")
        
        with self.assertRaises(DocumentConverterError):
            converter.convert_file(unsupported_file)
    
    @patch('doc2md.converter.DocumentConverter')
    def test_convert_file_success(self, mock_doc_converter):
        """测试成功转换文件"""
        # 创建测试文件
        test_file = self.temp_dir / "test.docx"
        test_file.write_text("fake docx content")
        
        # 模拟Docling转换结果
        mock_result = Mock()
        mock_document = Mock()
        mock_document.export_to_markdown.return_value = "# Test Document\n\nContent here."
        mock_document.pictures = []
        mock_result.document = mock_document
        
        mock_converter_instance = Mock()
        mock_converter_instance.convert.return_value = mock_result
        mock_doc_converter.return_value = mock_converter_instance
        
        converter = Doc2MDConverter(self.config)
        
        # 执行转换
        output_path = converter.convert_file(test_file)
        
        # 验证结果
        self.assertTrue(output_path.exists())
        self.assertEqual(output_path.suffix, '.md')
        
        # 验证内容
        content = output_path.read_text(encoding='utf-8')
        self.assertIn("# Test Document", content)
    
    def test_batch_convert(self):
        """测试批量转换"""
        with patch('doc2md.converter.DocumentConverter'):
            converter = Doc2MDConverter(self.config)
            
            # 创建测试文件
            files = []
            for i in range(3):
                test_file = self.temp_dir / f"test_{i}.docx"
                test_file.write_text(f"content {i}")
                files.append(test_file)
            
            # 模拟convert_file方法
            def mock_convert_file(input_path, output_path=None):
                if output_path is None:
                    output_path = input_path.with_suffix('.md')
                output_path.write_text(f"converted {input_path.name}")
                return output_path
            
            converter.convert_file = Mock(side_effect=mock_convert_file)
            
            # 执行批量转换
            results = converter.convert_batch(files)
            
            # 验证结果
            self.assertEqual(len(results), 3)
            for result in results:
                self.assertTrue(result.exists())
                self.assertEqual(result.suffix, '.md')


if __name__ == '__main__':
    unittest.main()
