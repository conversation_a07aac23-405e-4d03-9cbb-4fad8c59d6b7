# 文档转换工具 (Doc2MD)

一个基于Docling的离线文档转换工具，支持将PPTX和DOCX文件转换为Markdown格式。

## 功能特性

- 🔄 支持PPTX和DOCX文件转换为Markdown
- 📁 支持单文件和批量处理
- ⚙️ 可配置的转换选项
- 📊 详细的处理进度显示
- 🚫 完全离线工作，无需网络连接
- 📝 保持文档结构和格式

## 快速安装

### 自动安装（推荐）

运行自动安装脚本：

```bash
python install.py
```

这将自动：
- 检查Python版本
- 创建虚拟环境
- 安装所有依赖
- 创建激活脚本

### 手动安装

1. 克隆项目：
```bash
git clone <repository-url>
cd docling
```

2. 创建虚拟环境：
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

## 系统要求

- Python 3.8 或更高版本
- 支持的操作系统：Windows、macOS、Linux
- 内存：建议至少 4GB RAM
- 磁盘空间：至少 1GB 可用空间

## 使用方法

### 激活环境

首先激活虚拟环境：

**Windows:**
```bash
activate.bat
```

**Linux/Mac:**
```bash
./activate.sh
```

### 基本用法

```bash
# 转换单个文件
python doc2md.py input.docx

# 转换单个文件并指定输出路径
python doc2md.py input.pptx -o output.md

# 批量转换目录中的所有文档
python doc2md.py /path/to/documents/ -o /path/to/output/

# 递归处理子目录
python doc2md.py /path/to/documents/ -r
```

### 高级选项

```bash
# 保留图片（默认开启）
python doc2md.py input.docx --preserve-images

# 不保留图片
python doc2md.py input.docx --no-preserve-images

# 包含文档元数据
python doc2md.py input.docx --include-metadata

# 使用配置文件
python doc2md.py input.docx -c config.yaml

# 显示详细处理信息
python doc2md.py input.docx -v

# 设置并发线程数
python doc2md.py documents/ --max-workers 8
```

### 图片处理

工具会自动：
1. 提取PPTX/DOCX中的图片
2. 保存到 `{文档名}_images/` 目录
3. 在Markdown中使用相对路径引用

例如，转换 `presentation.pptx` 会产生：
```
presentation.md
presentation_images/
  ├── presentation_image_001.png
  ├── presentation_image_002.jpg
  └── ...
```

## 配置

创建 `config.yaml` 文件来自定义转换选项：

```yaml
output:
  preserve_images: true
  extract_tables: true
  include_metadata: false

processing:
  max_workers: 4
  timeout: 300

logging:
  level: INFO
  file: doc2md.log
```

## 支持的文件格式

- **输入格式**: `.docx`, `.pptx`
- **输出格式**: `.md` (Markdown)

## 测试

运行测试套件：

```bash
# 运行所有测试
python run_tests.py

# 运行特定测试
python run_tests.py tests.test_converter.TestDoc2MDConverter.test_convert_file_success
```

## 故障排除

### 常见问题

1. **ImportError: 无法导入Docling库**
   - 确保已激活虚拟环境
   - 重新运行 `pip install -r requirements.txt`

2. **转换失败：不支持的文件格式**
   - 确保文件是 `.docx` 或 `.pptx` 格式
   - 检查文件是否损坏

3. **图片提取失败**
   - 检查磁盘空间是否充足
   - 确保有写入权限
   - 运行快速测试：`python quick_test.py`
   - 确保安装了 `docling-core`：`pip install docling-core`

4. **内存不足**
   - 减少 `--max-workers` 参数
   - 分批处理大文件

### 日志调试

启用详细日志：

```bash
python doc2md.py input.docx -v
```

或修改 `config.yaml` 中的日志级别：

```yaml
logging:
  level: "DEBUG"
  file: "doc2md.log"
```

## 贡献

欢迎贡献代码！请：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
