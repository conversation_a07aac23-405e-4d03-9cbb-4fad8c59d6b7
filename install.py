#!/usr/bin/env python3
"""
Doc2MD 安装脚本
自动设置虚拟环境并安装依赖
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, cwd=None, check=True):
    """运行命令"""
    print(f"执行命令: {' '.join(command)}")
    try:
        result = subprocess.run(
            command, 
            cwd=cwd, 
            check=check, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        sys.exit(1)
    
    print(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")


def create_virtual_environment():
    """创建虚拟环境"""
    venv_path = Path(".venv")
    
    if venv_path.exists():
        print("虚拟环境已存在，跳过创建")
        return venv_path
    
    print("创建虚拟环境...")
    run_command([sys.executable, "-m", "venv", str(venv_path)])
    
    return venv_path


def get_venv_python(venv_path):
    """获取虚拟环境中的Python路径"""
    if platform.system() == "Windows":
        return venv_path / "Scripts" / "python.exe"
    else:
        return venv_path / "bin" / "python"


def install_dependencies(venv_python):
    """安装依赖"""
    print("安装依赖包...")
    
    # 升级pip
    run_command([str(venv_python), "-m", "pip", "install", "--upgrade", "pip"])
    
    # 安装requirements.txt中的依赖
    requirements_file = Path("requirements.txt")
    if requirements_file.exists():
        run_command([str(venv_python), "-m", "pip", "install", "-r", str(requirements_file)])
    else:
        print("警告: requirements.txt文件不存在")
    
    # 安装当前包（开发模式）
    run_command([str(venv_python), "-m", "pip", "install", "-e", "."])


def create_activation_script():
    """创建激活脚本"""
    if platform.system() == "Windows":
        script_content = """@echo off
echo 激活Doc2MD虚拟环境...
call .venv\\Scripts\\activate.bat
echo 虚拟环境已激活！
echo 使用 'python doc2md.py --help' 查看帮助信息
cmd /k
"""
        script_path = Path("activate.bat")
    else:
        script_content = """#!/bin/bash
echo "激活Doc2MD虚拟环境..."
source .venv/bin/activate
echo "虚拟环境已激活！"
echo "使用 'python doc2md.py --help' 查看帮助信息"
exec "$SHELL"
"""
        script_path = Path("activate.sh")
    
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    if not platform.system() == "Windows":
        os.chmod(script_path, 0o755)
    
    print(f"创建激活脚本: {script_path}")


def main():
    """主函数"""
    print("=== Doc2MD 安装程序 ===\n")
    
    # 检查Python版本
    check_python_version()
    
    # 创建虚拟环境
    venv_path = create_virtual_environment()
    
    # 获取虚拟环境Python路径
    venv_python = get_venv_python(venv_path)
    
    if not venv_python.exists():
        print(f"错误: 虚拟环境Python不存在: {venv_python}")
        sys.exit(1)
    
    # 安装依赖
    install_dependencies(venv_python)
    
    # 创建激活脚本
    create_activation_script()
    
    print("\n=== 安装完成 ===")
    print("使用方法:")
    
    if platform.system() == "Windows":
        print("1. 运行 activate.bat 激活虚拟环境")
    else:
        print("1. 运行 ./activate.sh 激活虚拟环境")
    
    print("2. 使用命令: python doc2md.py --help")
    print("\n示例:")
    print("  python doc2md.py document.docx")
    print("  python doc2md.py documents/ -o output/ -r")


if __name__ == "__main__":
    main()
