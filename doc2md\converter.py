"""
核心文档转换模块
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Any, List
import tempfile
import shutil

try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PdfPipelineOptions
    from docling.document_converter import PdfFormatOption
except ImportError as e:
    raise ImportError(f"无法导入Docling库，请确保已正确安装: {e}")

from .exceptions import (
    DocumentConverterError,
    ImageExtractionError,
    UnsupportedFormatError,
    OutputError
)


class Doc2MDConverter:
    """文档到Markdown转换器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化转换器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化Docling转换器
        self._init_docling_converter()
    
    def _init_docling_converter(self):
        """初始化Docling文档转换器"""
        try:
            # 配置处理选项，启用图片生成
            pdf_options = PdfPipelineOptions()
            pdf_options.do_ocr = self.config.get('docling', {}).get('enable_ocr', False)

            # 启用图片生成选项
            if self.config.get('output', {}).get('preserve_images', True):
                pdf_options.images_scale = 2.0  # 高质量图片
                pdf_options.generate_page_images = True
                pdf_options.generate_picture_images = True
                pdf_options.generate_table_images = True

            # 创建转换器
            self.converter = DocumentConverter(
                format_options={
                    InputFormat.PDF: PdfFormatOption(pipeline_options=pdf_options)
                }
            )

            self.logger.info("Docling转换器初始化成功")

        except Exception as e:
            self.logger.error(f"初始化Docling转换器失败: {e}")
            raise DocumentConverterError(f"转换器初始化失败: {e}")
    
    def convert_file(self, input_path: Path, output_path: Optional[Path] = None) -> Path:
        """
        转换单个文件

        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径（可选）

        Returns:
            输出文件路径

        Raises:
            DocumentConverterError: 转换失败时抛出
        """
        input_path = Path(input_path)

        # 检查输入文件
        if not input_path.exists():
            raise DocumentConverterError(f"输入文件不存在: {input_path}")

        if not self._is_supported_format(input_path):
            raise DocumentConverterError(f"不支持的文件格式: {input_path.suffix}")

        # 确定输出路径
        if output_path is None:
            output_path = input_path.with_suffix('.md')
        else:
            output_path = Path(output_path)
            if output_path.is_dir():
                output_path = output_path / f"{input_path.stem}.md"

        # 创建图片输出目录（Excel 不创建）
        images_dir = None
        if (
            input_path.suffix.lower() != '.xlsx'
            and self.config.get('output', {}).get('preserve_images', True)
        ):
            images_dir = output_path.parent / f"{output_path.stem}_images"
            images_dir.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"开始转换: {input_path} -> {output_path}")

        try:
            # Excel 文件专用转换
            if input_path.suffix.lower() == '.xlsx':
                markdown_content = self._convert_xlsx_to_markdown(input_path)

                # 后处理Markdown内容
                processed_content = self._post_process_markdown(
                    markdown_content, input_path, None, None
                )

                # 确保输出目录存在
                output_path.parent.mkdir(parents=True, exist_ok=True)

                # 写入输出文件
                encoding = self.config.get('output', {}).get('encoding', 'utf-8')
                with open(output_path, 'w', encoding=encoding) as f:
                    f.write(processed_content)

                self.logger.info(f"转换完成: {output_path}")
                return output_path

            # 使用Docling转换文档（DOCX/PPTX）
            result = self.converter.convert(str(input_path))

            # 提取并保存图片
            image_mappings = {}
            if images_dir and self.config.get('output', {}).get('preserve_images', True):
                image_mappings = self._extract_images(result.document, images_dir, input_path.stem)

            # 提取Markdown内容
            markdown_content = result.document.export_to_markdown()

            # 后处理Markdown内容（包括图片引用）
            processed_content = self._post_process_markdown(
                markdown_content, input_path, image_mappings, images_dir
            )

            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 写入输出文件
            encoding = self.config.get('output', {}).get('encoding', 'utf-8')
            with open(output_path, 'w', encoding=encoding) as f:
                f.write(processed_content)

            self.logger.info(f"转换完成: {output_path}")
            if image_mappings:
                self.logger.info(f"提取了 {len(image_mappings)} 张图片到: {images_dir}")

            return output_path

        except Exception as e:
            self.logger.error(f"转换文件失败 {input_path}: {e}")
            raise DocumentConverterError(f"转换失败: {e}")
    
    def convert_batch(self, input_paths: List[Path], output_dir: Optional[Path] = None) -> List[Path]:
        """
        批量转换文件
        
        Args:
            input_paths: 输入文件路径列表
            output_dir: 输出目录（可选）
            
        Returns:
            输出文件路径列表
        """
        output_paths = []
        
        for input_path in input_paths:
            try:
                if output_dir:
                    output_path = output_dir / f"{input_path.stem}.md"
                else:
                    output_path = None
                
                result_path = self.convert_file(input_path, output_path)
                output_paths.append(result_path)
                
            except DocumentConverterError as e:
                self.logger.error(f"跳过文件 {input_path}: {e}")
                continue
        
        return output_paths
    
    def _is_supported_format(self, file_path: Path) -> bool:
        """检查是否为支持的文件格式"""
        supported_extensions = {'.docx', '.pptx', '.xlsx'}
        return file_path.suffix.lower() in supported_extensions
    
    def _extract_images(self, document, images_dir: Path, doc_name: str) -> Dict[str, str]:
        """
        提取并保存图片

        Args:
            document: Docling文档对象
            images_dir: 图片保存目录
            doc_name: 文档名称

        Returns:
            图片ID到文件路径的映射
        """
        image_mappings = {}
        picture_counter = 0
        table_counter = 0

        try:
            # 导入Docling的类型
            from docling_core.types.doc import PictureItem, TableItem

            # 遍历文档中的所有元素
            for element, _level in document.iterate_items():
                try:
                    image_path = None
                    relative_path = None
                    element_id = None

                    if isinstance(element, PictureItem):
                        picture_counter += 1
                        image_filename = f"{doc_name}_picture_{picture_counter:03d}.png"
                        image_path = images_dir / image_filename
                        relative_path = f"{images_dir.name}/{image_filename}"
                        element_id = getattr(element, 'id', f'picture_{picture_counter}')

                        # 使用正确的API获取图片
                        if hasattr(element, 'get_image'):
                            pil_image = element.get_image(document)
                            if pil_image:
                                pil_image.save(image_path, "PNG")
                                self.logger.debug(f"保存图片: {image_path}")

                    elif isinstance(element, TableItem):
                        table_counter += 1
                        image_filename = f"{doc_name}_table_{table_counter:03d}.png"
                        image_path = images_dir / image_filename
                        relative_path = f"{images_dir.name}/{image_filename}"
                        element_id = getattr(element, 'id', f'table_{table_counter}')

                        # 使用正确的API获取表格图片
                        if hasattr(element, 'get_image'):
                            pil_image = element.get_image(document)
                            if pil_image:
                                pil_image.save(image_path, "PNG")
                                self.logger.debug(f"保存表格图片: {image_path}")

                    # 记录映射关系
                    if element_id and relative_path and image_path and image_path.exists():
                        image_mappings[element_id] = relative_path

                except Exception as e:
                    self.logger.warning(f"处理元素失败: {e}")
                    continue

        except ImportError as e:
            self.logger.error(f"无法导入Docling核心类型: {e}")
        except Exception as e:
            self.logger.error(f"提取图片时发生错误: {e}")

        if image_mappings:
            self.logger.info(f"成功提取 {len(image_mappings)} 个图片/表格")
        else:
            self.logger.info("未找到可提取的图片或表格")

        return image_mappings

    def _get_image_extension(self, picture) -> str:
        """获取图片文件扩展名"""
        # 尝试从图片对象获取格式信息
        if hasattr(picture, 'format'):
            format_map = {
                'PNG': '.png',
                'JPEG': '.jpg',
                'JPG': '.jpg',
                'GIF': '.gif',
                'BMP': '.bmp',
                'TIFF': '.tiff'
            }
            return format_map.get(picture.format.upper(), '.png')

        # 默认使用PNG格式
        return '.png'

    def _post_process_markdown(self, markdown_content: str, source_path: Path,
                             image_mappings: Dict[str, str] = None,
                             images_dir: Path = None) -> str:
        """
        后处理Markdown内容

        Args:
            markdown_content: 原始Markdown内容
            source_path: 源文件路径
            image_mappings: 图片ID到路径的映射
            images_dir: 图片目录

        Returns:
            处理后的Markdown内容
        """
        # 添加文档头部信息
        if self.config.get('output', {}).get('include_metadata', False):
            header = f"# {source_path.stem}\n\n"
            header += f"**源文件**: {source_path.name}  \n"
            header += f"**转换时间**: {self._get_current_time()}  \n\n"
            header += "---\n\n"
            markdown_content = header + markdown_content

        # 处理图片引用
        if image_mappings:
            markdown_content = self._update_image_references(markdown_content, image_mappings)

        # 清理和格式化内容
        markdown_content = self._clean_markdown(markdown_content)

        return markdown_content

    def _update_image_references(self, markdown_content: str, image_mappings: Dict[str, str]) -> str:
        """
        更新Markdown中的图片引用

        Args:
            markdown_content: Markdown内容
            image_mappings: 图片ID到路径的映射

        Returns:
            更新后的Markdown内容
        """
        import re

        # 查找并替换图片引用
        # 匹配类似 ![alt](image_id) 或 ![](image_id) 的模式
        def replace_image_ref(match):
            alt_text = match.group(1) or "图片"
            image_ref = match.group(2)

            # 查找对应的图片路径
            for image_id, image_path in image_mappings.items():
                if image_id in image_ref or image_ref in image_id:
                    return f"![{alt_text}]({image_path})"

            # 如果没找到对应的图片，保持原样
            return match.group(0)

        # 使用正则表达式替换图片引用
        pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        markdown_content = re.sub(pattern, replace_image_ref, markdown_content)

        return markdown_content

    def _clean_markdown(self, content: str) -> str:
        """清理Markdown内容"""
        # 移除多余的空行
        lines = content.split('\n')
        cleaned_lines = []
        prev_empty = False
        
        for line in lines:
            is_empty = not line.strip()
            if is_empty and prev_empty:
                continue
            cleaned_lines.append(line)
            prev_empty = is_empty
        
        return '\n'.join(cleaned_lines)
    
    def _convert_xlsx_to_markdown(self, xlsx_path: Path) -> str:
        """将 XLSX 工作簿转换为 Markdown（每个工作表一个表格）。"""
        try:
            from openpyxl import load_workbook
        except ImportError as e:
            raise DocumentConverterError(
                "缺少 openpyxl 依赖，请先安装：pip install openpyxl"
            ) from e

        wb = load_workbook(filename=str(xlsx_path), data_only=True)
        sections: List[str] = []

        for ws in wb.worksheets:
            max_row, max_col = self._detect_used_range(ws)
            if max_row == 0 or max_col == 0:
                self.logger.info(f"工作表为空，跳过: {ws.title}")
                continue

            sections.append(f"## {ws.title}\n")

            # 读取数据区域
            rows = []
            for r in ws.iter_rows(min_row=1, max_row=max_row, min_col=1, max_col=max_col, values_only=True):
                rows.append([self._format_cell(v) for v in r])

            if not rows:
                continue

            header = rows[0]
            data_rows = rows[1:] if len(rows) > 1 else []

            sections.append(self._markdown_table(header, data_rows))
            sections.append("")

        if not sections:
            return f"_工作簿 {xlsx_path.name} 未检测到可用数据_\n"

        return "\n".join(sections)

    def _detect_used_range(self, ws) -> (int, int):
        """检测工作表实际使用区域，返回 (max_row, max_col)。"""
        max_row = 0
        max_col = 0
        for row in ws.iter_rows():
            row_has_value = False
            last_col_with_value = 0
            for idx, cell in enumerate(row, start=1):
                val = getattr(cell, 'value', None)
                if val is not None and str(val).strip() != "":
                    row_has_value = True
                    last_col_with_value = idx
            if row_has_value:
                max_row = row[0].row
                if last_col_with_value > max_col:
                    max_col = last_col_with_value
        return max_row, max_col

    def _format_cell(self, value) -> str:
        """将单元格值格式化为适合 Markdown 的文本。"""
        if value is None:
            return ""
        text = str(value)
        text = text.replace("|", "\\|")
        text = text.replace("\n", "<br>")
        return text

    def _markdown_table(self, header: List[str], rows: List[List[str]]) -> str:
        """根据表头和数据行生成 Markdown 表格文本。"""
        # 如果表头全空，则生成默认列名
        if not any((h or '').strip() for h in header):
            header = [f"列{i+1}" for i in range(len(header))]

        def row_line(cols: List[str]) -> str:
            safe_cols = [(c or '') for c in cols]
            return "| " + " | ".join(safe_cols) + " |"

        lines = [row_line(header)]
        lines.append("| " + " | ".join(["---"] * len(header)) + " |")
        for r in rows:
            # 对齐列数
            if len(r) < len(header):
                r = r + [""] * (len(header) - len(r))
            elif len(r) > len(header):
                r = r[:len(header)]
            lines.append(row_line(r))
        return "\n".join(lines)
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
