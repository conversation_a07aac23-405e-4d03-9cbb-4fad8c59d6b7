"""
Doc2MD 安装脚本
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# 读取requirements文件
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text(encoding="utf-8").strip().split("\n")
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith("#")]

setup(
    name="doc2md",
    version="0.1.0",
    description="基于Docling的PPTX/DOCX到Markdown转换工具",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Doc2MD Team",
    author_email="<EMAIL>",
    url="https://github.com/your-username/doc2md",
    packages=find_packages(),
    include_package_data=True,
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "doc2md=doc2md.cli:main",
        ],
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Office Suites",
        "Topic :: Text Processing :: Markup",
        "Topic :: Utilities",
    ],
    python_requires=">=3.8",
    keywords="docx pptx markdown converter docling office documents",
    project_urls={
        "Bug Reports": "https://github.com/your-username/doc2md/issues",
        "Source": "https://github.com/your-username/doc2md",
        "Documentation": "https://github.com/your-username/doc2md/blob/main/README.md",
    },
)
