#!/usr/bin/env python3
"""
Doc2MD - 基于Docling的文档转换工具
将PPTX和DOCX文件转换为Markdown格式
"""

import click
import logging
from pathlib import Path
from typing import List, Optional
from rich.console import Console
from rich.progress import Progress, TaskID
from rich.table import Table
from rich import print as rprint

from doc2md.config import ConfigManager
from doc2md.converter import Doc2MDConverter
from doc2md.exceptions import DocumentConverterError


console = Console()


@click.command()
@click.argument('input_path', type=click.Path(exists=True, path_type=Path))
@click.option('-o', '--output', type=click.Path(path_type=Path), 
              help='输出文件或目录路径')
@click.option('-c', '--config', type=click.Path(exists=True, path_type=Path),
              help='配置文件路径')
@click.option('-r', '--recursive', is_flag=True,
              help='递归处理子目录')
@click.option('-v', '--verbose', is_flag=True,
              help='显示详细信息')
@click.option('--preserve-images/--no-preserve-images', default=True,
              help='是否保留图片（默认：是）')
@click.option('--include-metadata/--no-include-metadata', default=False,
              help='是否包含元数据（默认：否）')
@click.option('--max-workers', type=int, default=4,
              help='最大并发工作线程数（默认：4）')
def main(input_path: Path, output: Optional[Path], config: Optional[Path],
         recursive: bool, verbose: bool, preserve_images: bool,
         include_metadata: bool, max_workers: int):
    """
    Doc2MD - 文档转换工具
    
    将PPTX和DOCX文件转换为Markdown格式。
    
    INPUT_PATH: 输入文件或目录路径
    """
    
    # 显示欢迎信息
    console.print("\n[bold blue]Doc2MD - 文档转换工具[/bold blue]", justify="center")
    console.print("[dim]基于Docling的PPTX/DOCX到Markdown转换器[/dim]\n", justify="center")
    
    try:
        # 加载配置
        config_manager = ConfigManager(config)
        
        # 命令行参数覆盖配置
        if preserve_images is not None:
            config_manager.set('output.preserve_images', preserve_images)
        if include_metadata is not None:
            config_manager.set('output.include_metadata', include_metadata)
        if max_workers is not None:
            config_manager.set('processing.max_workers', max_workers)
        if recursive is not None:
            config_manager.set('processing.recursive', recursive)
        
        # 设置日志级别
        if verbose:
            config_manager.set('logging.level', 'DEBUG')
        
        # 初始化日志
        config_manager.setup_logging()
        
        # 创建转换器
        converter = Doc2MDConverter(config_manager.config)
        
        # 获取要处理的文件列表
        input_files = get_input_files(input_path, recursive)
        
        if not input_files:
            console.print("[yellow]警告: 未找到支持的文档文件[/yellow]")
            return
        
        # 显示处理信息
        show_processing_info(input_files, output, config_manager.config)
        
        # 执行转换
        success_count = process_files(converter, input_files, output)
        
        # 显示结果
        show_results(success_count, len(input_files))
        
    except Exception as e:
        console.print(f"[red]错误: {e}[/red]")
        if verbose:
            console.print_exception()
        raise click.Abort()


def get_input_files(input_path: Path, recursive: bool) -> List[Path]:
    """获取要处理的文件列表"""
    supported_extensions = {'.docx', '.pptx', '.xlsx'}
    files = []
    
    if input_path.is_file():
        if input_path.suffix.lower() in supported_extensions:
            files.append(input_path)
    elif input_path.is_dir():
        pattern = "**/*" if recursive else "*"
        for file_path in input_path.glob(pattern):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                files.append(file_path)
    
    return sorted(files)


def show_processing_info(files: List[Path], output: Optional[Path], config: dict):
    """显示处理信息"""
    table = Table(title="处理信息")
    table.add_column("项目", style="cyan")
    table.add_column("值", style="green")
    
    table.add_row("文件数量", str(len(files)))
    table.add_row("输出路径", str(output) if output else "与输入文件同目录")
    table.add_row("保留图片", "是" if config.get('output', {}).get('preserve_images') else "否")
    table.add_row("包含元数据", "是" if config.get('output', {}).get('include_metadata') else "否")
    table.add_row("并发线程", str(config.get('processing', {}).get('max_workers', 4)))
    
    console.print(table)
    console.print()


def process_files(converter: Doc2MDConverter, files: List[Path], 
                 output_dir: Optional[Path]) -> int:
    """处理文件列表"""
    success_count = 0
    
    with Progress() as progress:
        task = progress.add_task("[green]转换文档...", total=len(files))
        
        for file_path in files:
            try:
                progress.update(task, description=f"[green]转换: {file_path.name}")
                
                # 确定输出路径
                if output_dir:
                    if output_dir.is_dir() or len(files) > 1:
                        output_path = output_dir / f"{file_path.stem}.md"
                    else:
                        output_path = output_dir
                else:
                    output_path = None
                
                # 执行转换
                result_path = converter.convert_file(file_path, output_path)
                
                console.print(f"[green]✓[/green] {file_path.name} -> {result_path.name}")
                success_count += 1
                
            except DocumentConverterError as e:
                console.print(f"[red]✗[/red] {file_path.name}: {e}")
            except Exception as e:
                console.print(f"[red]✗[/red] {file_path.name}: 未知错误 - {e}")
            
            progress.advance(task)
    
    return success_count


def show_results(success_count: int, total_count: int):
    """显示处理结果"""
    console.print()
    
    if success_count == total_count:
        console.print(f"[green]✅ 全部完成！成功转换 {success_count} 个文件[/green]")
    elif success_count > 0:
        console.print(f"[yellow]⚠️  部分完成：成功 {success_count}/{total_count} 个文件[/yellow]")
    else:
        console.print(f"[red]❌ 转换失败：0/{total_count} 个文件[/red]")


if __name__ == '__main__':
    main()
