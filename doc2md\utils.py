"""
工具函数模块
"""

import os
import logging
from pathlib import Path
from typing import List, Generator, Optional
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor


def find_documents(root_path: Path, recursive: bool = False, 
                  extensions: Optional[List[str]] = None) -> List[Path]:
    """
    查找文档文件
    
    Args:
        root_path: 根目录路径
        recursive: 是否递归搜索
        extensions: 支持的文件扩展名列表
        
    Returns:
        文档文件路径列表
    """
    if extensions is None:
        extensions = ['.docx', '.pptx']
    
    extensions = [ext.lower() for ext in extensions]
    files = []
    
    if root_path.is_file():
        if root_path.suffix.lower() in extensions:
            files.append(root_path)
    elif root_path.is_dir():
        pattern = "**/*" if recursive else "*"
        for file_path in root_path.glob(pattern):
            if file_path.is_file() and file_path.suffix.lower() in extensions:
                files.append(file_path)
    
    return sorted(files)


def ensure_directory(path: Path) -> Path:
    """
    确保目录存在
    
    Args:
        path: 目录路径
        
    Returns:
        目录路径
    """
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_relative_path(file_path: Path, base_path: Path) -> Path:
    """
    获取相对路径
    
    Args:
        file_path: 文件路径
        base_path: 基础路径
        
    Returns:
        相对路径
    """
    try:
        return file_path.relative_to(base_path)
    except ValueError:
        return file_path


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        格式化的文件大小字符串
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def get_file_info(file_path: Path) -> dict:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件信息字典
    """
    try:
        stat = file_path.stat()
        return {
            'name': file_path.name,
            'size': stat.st_size,
            'size_formatted': format_file_size(stat.st_size),
            'modified': stat.st_mtime,
            'extension': file_path.suffix.lower()
        }
    except OSError as e:
        logging.warning(f"获取文件信息失败 {file_path}: {e}")
        return {
            'name': file_path.name,
            'size': 0,
            'size_formatted': '0 B',
            'modified': 0,
            'extension': file_path.suffix.lower()
        }


def batch_process(items: List, process_func, max_workers: int = 4, 
                 progress_callback=None) -> List:
    """
    批量处理项目
    
    Args:
        items: 要处理的项目列表
        process_func: 处理函数
        max_workers: 最大工作线程数
        progress_callback: 进度回调函数
        
    Returns:
        处理结果列表
    """
    results = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_item = {
            executor.submit(process_func, item): item 
            for item in items
        }
        
        # 收集结果
        for i, future in enumerate(concurrent.futures.as_completed(future_to_item)):
            item = future_to_item[future]
            try:
                result = future.result()
                results.append(result)
                
                if progress_callback:
                    progress_callback(i + 1, len(items), item, result)
                    
            except Exception as e:
                logging.error(f"处理项目失败 {item}: {e}")
                results.append(None)
                
                if progress_callback:
                    progress_callback(i + 1, len(items), item, None)
    
    return results


def validate_input_path(path: Path) -> bool:
    """
    验证输入路径
    
    Args:
        path: 输入路径
        
    Returns:
        是否有效
    """
    if not path.exists():
        return False
    
    if path.is_file():
        return path.suffix.lower() in ['.docx', '.pptx']
    
    if path.is_dir():
        # 检查目录是否包含支持的文件
        for file_path in path.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in ['.docx', '.pptx']:
                return True
        return False
    
    return False


def create_output_structure(input_path: Path, output_path: Path, 
                          preserve_structure: bool = True) -> Path:
    """
    创建输出目录结构
    
    Args:
        input_path: 输入路径
        output_path: 输出路径
        preserve_structure: 是否保持目录结构
        
    Returns:
        实际输出路径
    """
    if input_path.is_file():
        if output_path.is_dir():
            return output_path / f"{input_path.stem}.md"
        else:
            ensure_directory(output_path.parent)
            return output_path
    
    if input_path.is_dir():
        ensure_directory(output_path)
        return output_path
    
    return output_path
