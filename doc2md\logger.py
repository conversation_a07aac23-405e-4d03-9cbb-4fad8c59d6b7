"""
日志配置模块
"""

import logging
import sys
from pathlib import Path
from typing import Optional
from rich.logging import RichHandler


class LoggerSetup:
    """日志设置类"""
    
    @staticmethod
    def setup_logger(name: str = "doc2md", 
                    level: str = "INFO",
                    log_file: Optional[Path] = None,
                    console: bool = True,
                    rich_console: bool = True) -> logging.Logger:
        """
        设置日志记录器
        
        Args:
            name: 日志记录器名称
            level: 日志级别
            log_file: 日志文件路径
            console: 是否输出到控制台
            rich_console: 是否使用Rich格式化控制台输出
            
        Returns:
            配置好的日志记录器
        """
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper()))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        if console:
            if rich_console:
                console_handler = RichHandler(
                    rich_tracebacks=True,
                    show_path=False,
                    show_time=False
                )
            else:
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setFormatter(formatter)
            
            console_handler.setLevel(getattr(logging, level.upper()))
            logger.addHandler(console_handler)
        
        # 文件处理器
        if log_file:
            log_file = Path(log_file)
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.DEBUG)  # 文件记录所有级别
            logger.addHandler(file_handler)
        
        return logger
    
    @staticmethod
    def setup_from_config(config: dict) -> logging.Logger:
        """
        从配置字典设置日志
        
        Args:
            config: 配置字典
            
        Returns:
            配置好的日志记录器
        """
        log_config = config.get('logging', {})
        
        return LoggerSetup.setup_logger(
            level=log_config.get('level', 'INFO'),
            log_file=log_config.get('file'),
            console=log_config.get('console', True),
            rich_console=log_config.get('rich_console', True)
        )


class ProgressLogger:
    """进度日志记录器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.total_files = 0
        self.processed_files = 0
        self.success_files = 0
        self.failed_files = 0
    
    def start_batch(self, total_files: int):
        """开始批量处理"""
        self.total_files = total_files
        self.processed_files = 0
        self.success_files = 0
        self.failed_files = 0
        self.logger.info(f"开始批量处理 {total_files} 个文件")
    
    def file_started(self, file_path: Path):
        """文件处理开始"""
        self.logger.debug(f"开始处理文件: {file_path}")
    
    def file_completed(self, file_path: Path, success: bool, message: str = ""):
        """文件处理完成"""
        self.processed_files += 1
        
        if success:
            self.success_files += 1
            self.logger.info(f"✓ 成功处理: {file_path.name}")
        else:
            self.failed_files += 1
            self.logger.error(f"✗ 处理失败: {file_path.name} - {message}")
        
        # 记录进度
        progress = (self.processed_files / self.total_files) * 100
        self.logger.debug(f"进度: {self.processed_files}/{self.total_files} ({progress:.1f}%)")
    
    def batch_completed(self):
        """批量处理完成"""
        self.logger.info(
            f"批量处理完成: 成功 {self.success_files}, 失败 {self.failed_files}, "
            f"总计 {self.total_files}"
        )


def get_logger(name: str = "doc2md") -> logging.Logger:
    """获取日志记录器"""
    return logging.getLogger(name)
